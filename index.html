<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PornTubeX - Premium Adult Content</title>
    <meta name="description" content="PornTubeX - Premium adult content platform with high-quality videos">
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.html" class="brand-link">
                        <h1 class="brand-title">PornTubeX</h1>
                    </a>
                </div>
                
                <div class="nav-search">
                    <form class="search-form" id="searchForm">
                        <input type="text" class="search-input" placeholder="Search videos..." id="searchInput">
                        <button type="submit" class="search-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                        </button>
                    </form>
                </div>
                
                <div class="nav-menu">
                    <a href="#" class="nav-link">Browse</a>
                    <a href="#" class="nav-link">Categories</a>
                    <a href="#" class="nav-link">Popular</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h2 class="hero-title">Premium Adult Content</h2>
                <p class="hero-subtitle">Discover high-quality, artistic adult content from verified creators</p>
            </div>
        </section>

        <!-- Filter Controls -->
        <section class="filter-section">
            <div class="container">
                <h3 class="section-title">Filter Content</h3>
                <div class="filter-controls">
                    <div class="filter-group">
                        <label for="categoryFilter">Category:</label>
                        <select id="categoryFilter">
                            <option value="all">All Categories</option>
                            <option value="new_videos">New Videos</option>
                            <option value="photos">Photo Gallery</option>
                            <option value="threesome">Threesome</option>
                            <option value="lesbian">Lesbian</option>
                            <option value="stepsister">Step Sister</option>
                            <option value="family">Family</option>
                            <option value="asian">Asian</option>
                            <option value="latina">Latina</option>
                            <option value="romantic">Romantic</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="typeFilter">Type:</label>
                        <select id="typeFilter">
                            <option value="all">All Types</option>
                            <option value="video">Videos Only</option>
                            <option value="photo">Photos Only</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="sortFilter">Sort By:</label>
                        <select id="sortFilter">
                            <option value="rating">Rating</option>
                            <option value="views">Views</option>
                            <option value="likes">Likes</option>
                            <option value="date">Upload Date</option>
                            <option value="title">Title</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="ratingFilter">Min Rating:</label>
                        <select id="ratingFilter">
                            <option value="0">Any Rating</option>
                            <option value="3">3+ Stars</option>
                            <option value="4">4+ Stars</option>
                            <option value="4.5">4.5+ Stars</option>
                        </select>
                    </div>

                    <button id="applyFilters" class="filter-btn">Apply Filters</button>
                    <button id="clearFilters" class="filter-btn secondary">Clear</button>
                </div>
            </div>
        </section>

        <!-- Featured Videos -->
        <section class="featured-section">
            <div class="container">
                <h3 class="section-title">Featured Videos</h3>
                <div class="videos-grid" id="featuredVideos">
                    <!-- Featured videos will be loaded dynamically -->
                </div>
            </div>
        </section>

        <!-- Photos Section -->
        <section class="photos-section">
            <div class="container">
                <h3 class="section-title">Photo Gallery</h3>
                <div class="photos-grid" id="photosGrid">
                    <!-- Photos will be loaded dynamically -->
                </div>
            </div>
        </section>

        <!-- Recent Videos -->
        <section class="recent-section">
            <div class="container">
                <h3 class="section-title">Recent Videos</h3>
                <div class="videos-grid" id="recentVideos">
                    <!-- Recent videos will be loaded dynamically -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>PornTubeX</h4>
                    <p>Premium adult content platform</p>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="#">Romantic</a></li>
                        <li><a href="#">Artistic</a></li>
                        <li><a href="#">Wellness</a></li>
                        <li><a href="#">Massage</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 PornTubeX. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/homepage.js"></script>
</body>
</html>
